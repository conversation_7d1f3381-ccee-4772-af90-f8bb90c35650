
/*
var folder = "assets/portrety/";

const folders = ["assets/kreativni-tvorba/", "assets/hudebni-video/", "assets/portrety/"];
folders.forEach(cesta => {
    let path =(cesta.split("assets/")[1])
    console.log(path)
    pathForClasss = ".content." + path.slice(0, -1)
    console.log(pathForClasss)
    xxx = (pathForClasss +" .galerie")
    

$.ajax({
    url : cesta,
    success: function (data) {
        $(data).find("a").attr("href", function (i, val) {
            if( val.match(/\.(jpe?g|png|gif)$/) ) { 
                console.log(pathForClasss +" .galerie");
                console.log(cesta + val)
                $(`${pathForClasss} .galerie`).append( "<img src='"+ cesta + val +"'>" );
            } 
        });
    }
});
});


*/
function hideAllContentDivs(){
    var contentDivs = document.getElementsByClassName('content');
    for (var i = 0; i < contentDivs.length; ++i) {
        var div = contentDivs[i];  
        div.style.display='none';
    }
  };

  const folders = ["assets/kreativni-tvorba/", "assets/portrety/"];

  async function getImages() {
    for (let i = 0; i < folders.length; i++) {
      const cesta = folders[i];
      let path =(cesta.split("assets/")[1])
      console.log(path)
      pathForClasss = ".content." + path.slice(0, -1)
      console.log(pathForClasss)
      xxx = (pathForClasss +" .galerie")
  
      await $.ajax({
        url : cesta,
        success: function (data) {
          $(data).find("a").attr("href", function (i, val) {
            if( val.match(/\.(jpe?g|png|gif)$/) ) { 
              console.log(pathForClasss +" .galerie");
              console.log(cesta + val)
              $(`${pathForClasss} .galerie`).append(`<img src="${cesta}${val}" loading="lazy">`);
            } 
          });
        }
      });
    }
  }
  
  function myFunction() {
    getImages();
    // Zde můžete provést další akce, které chcete po načtení stránky provést
}


document.addEventListener('DOMContentLoaded', function() {
  myFunction();
});

  