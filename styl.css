.centrovac{
    margin: 0px auto;
    max-width: 1080px;
    min-width: 250px;
    min-height: 1vh;
    background-color: black;
    z-index: 0;
}

@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap');
.montserrat {
font-family: 'Montserrat', sans-serif;
} 

@media only screen and (max-width: 1080px) {
    .centrovac{
        width: 100%;
    }
    .popis h2{
        font-size: 5vw;
    }
    .widefotka{
        width: 60%;
    }
    .heightfotka{
        width: 60%;
    }
    .galerie{
        display: grid;
        grid-template-columns: auto auto;
    }
  }

@media only screen and (min-width: 1080px) {
    .centrovac{
        width: 100%;
    }
    .popis h2{
        font-size: 3vw;
    }
    .widefotka{
        width: 50%;
    }
    .heightfotka{
        width: 50%;
    }
    .galerie{
        display: grid;
        grid-template-columns: auto auto auto;
    }
  }




body{
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
    background-color: black;
    
}

*{
    margin: 0;
    padding: 0;
    font-family: 'Montserrat', sans-serif;
}


div.sticky {
    position: sticky;
    top: 90%;
    padding: 15px;
    font-size: 20px;
    z-index: 1;
    width: 70px;
    left: 0;
    background-image: url(assets/zpet.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    mix-blend-mode: lighten;
  }

div.sticky a {
    color: #ffffff;
    text-decoration: none;
}


h1{
    margin: 0;
    font-size: 8em;
}

.logo img{
   width: 100%;
}

.obrazek img{
        position: absolute;
        right:  0vw; 
        bottom: 10vh;
        height: 22vh;
       
}
.obrazek{
    grid-row: 2;
    grid-column: 2/3;
    width: 100%;
    position: relative;
    z-index: 1;
}

header h2{
    text-align: left;
    font-size: 4em;
    /*
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: rgb(255, 255, 255);
    color: rgba(0,0,0,0);
    */
    z-index: 2;
}

.popis{
    margin-top: 9vh;
    grid-column: 2/3;
    grid-row: 2;
    z-index: 1;
    margin-left: 5%;
}

.nadpis{
    text-align: center;
    color: white;
    margin-bottom: 1em;
    font-size: 3vh;
}
.outline{
    
    text-shadow: 1px 1px 0 #ffffff, -1px -1px 0 #ffffff, 1px -1px 0 #ffffff,
    -1px 1px 0 #ffffff, 1px 1px 0 #ffffff;

    color: #000000;
    
}

.widefotka{
    margin: auto;
    box-sizing: border-box;
}

.paperTXT_wide{
    background-image: url(assets/papir.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    padding: 10%;
    position: relative;
}

.paperTXT_height{
    background-image: url(assets/papir_navysku.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    padding: 18%;
    position: relative;
}

.heightfotka{
    margin: auto;
    box-sizing: border-box;
    padding: 50px;
}

.izolepa_rightW{
    position: absolute;
    z-index: 3;
    mix-blend-mode: lighten;
    width: 40%;
    right: -5%;
    top: -2%;
}

.izolepa_leftW{
    position: absolute;
    z-index: 3;
    mix-blend-mode: lighten;
    width: 38%;
    left: -5%;
    bottom: -2%;
    transform: scaleX(-1);
    transform:rotate(190deg);
    }

    .izolepa_rightH{
        position: absolute;
        z-index: 3;
        mix-blend-mode: lighten;
        width: 35%;
        right: 0;
        top: 4%;
        transform:rotate(20deg);
    }
    
    .izolepa_leftH{
        position: absolute;
        z-index: 3;
        mix-blend-mode: lighten;
        width: 40%;
        left: -5%;
        top: 4%;
        transform: scaleX(-1);
        transform:rotate(330deg);
        }

article img{
    width: 100%;
}


article div.content.home img {
    -webkit-filter: grayscale(100%);
    transition: filter .2s ease-in-out;
}

article div.content.home img:hover {
    filter: none;
    -webkit-filter: grayscale(0);
}


.left{  
    grid-column: 1/2;
    grid-row: 1/3;
}
.right{  
    grid-column: 3/4;
    grid-row: 1/3;
}

.mrizka{
    display: grid;
    grid-template-columns: repeat(3,1fr);  
    grid-template-rows: auto auto auto;
}

header{
    display: grid;
    grid-template-rows: auto 1fr;
    grid-template-columns: 10% 80% 10%;
    grid-column: 1/4;
    grid-row: 1/2;
    color: white;
    height: 40vh;
}

article{
    grid-column: 1/4;
    grid-row: 2/3;
    height: auto;
    background-color: rgb(0, 0, 0);
}

footer{
    grid-column: 1/4;
    grid-row: 3/4;
    background-color: rgb(0, 0, 0);
    padding-top: 15vh;
    padding-bottom: 2vh;
     
}

footer a, footer p{
    color: white;
    text-decoration: none; 
    font-size: 1.2em;
}

footer a:hover
{
    color: rgba(203,9,20,1);
}

.gradient{
    background: linear-gradient(90deg, rgba(203,9,20,1) 0%, rgba(244,96,37,1) 42%, rgba(255,255,255,1) 100%);
    height: 1.5vh;
    position: absolute;
    width: 100%;
    top: 30vh;
    z-index: 0;
}

.galerie{
    
    grid-column: 1/4;
    grid-row: 2/3;
    background-color: rgb(0, 0, 0);
    margin: auto;
    width: 90%;
    grid-column-gap: 1vw;
    grid-row-gap: 1vw;
    padding-bottom: 10vh;
}
.galerie img{
    width: 100%;
    box-sizing: border-box
}

iframe {
    border: none;
 }

.v_kontejner{
    position: relative;
    width: 100%;
    padding-bottom: 56.25%;
    height: 0;
    grid-column: 1/4;
}

.video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.kontakt{
    text-align: center;
    color: white;
    font-size: 3em;
}

p{
    text-align: center;
    color: white;
}

